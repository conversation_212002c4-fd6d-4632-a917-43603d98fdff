<!-- Debug Info -->
<!-- <div class="debug-info mb-2 p-2 bg-light">
  <small>Debug: isLoggedIn={{isLoggedIn}}, currentUserId={{currentUserId}}, userId={{userId}}, canCreate={{isLoggedIn && currentUserId === userId}}</small>
  <button type="button" class="btn btn-sm btn-warning ms-2" (click)="testModal()">Test Modal</button>
</div> -->

<!-- Create Post Button (for logged-in users viewing their own profile) -->
<div class="create-post-section mb-3" *ngIf="isOwnProfile()">
  <button
    type="button"
    class="btn btn-primary create-post-btn"
    (click)="createPost()">
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
      <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" fill="currentColor"/>
    </svg>
    Create New Post
  </button>
</div>

<!-- Loader -->
<div *ngIf="serviceActivitiesLoading" class="text-center py-4">
  <app-spinner></app-spinner>
</div>

<!-- No Data Message -->
<div *ngIf="!serviceActivitiesLoading && serviceActivities.length === 0" class="empty-state">
  <div class="empty-state-content">
    <h2>No Service Activities Available</h2>
    <p>There are currently no service activities to display.</p>
  </div>
</div>

<!-- Activities List -->
<div *ngIf="!serviceActivitiesLoading && serviceActivities.length > 0">
  <div class="fc-card mb-3 p-4" *ngFor="let activity of serviceActivities">
    <div class="fc-share-thought">
      <div class="col-sm-12 d-flex justify-content-between">
        <div class="fc-post-user">
          <div class="user-img">
            <img [src]="getDisplayPhoto(activity)" alt="Profile Photo">
          </div>
          <div class="d-flex flex-column">
            <label class="fs-12">{{ getDisplayName(activity) }}</label>
            <span class="fs-12">{{ getDisplayRole(activity) }}</span>
          </div>
        </div>
        <div class="activity-actions">          
          <!-- More Options Menu -->
          <div class="dropdown">
            <button class="btn btn-sm btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
              <svg width="5" height="17" viewBox="0 0 5 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <ellipse cx="2.3042" cy="2.37209" rx="1.92725" ry="1.88771" fill="#C4C4C4" />
                <ellipse cx="2.3042" cy="8.03518" rx="1.92725" ry="1.88771" fill="#C4C4C4" />
                <ellipse cx="2.3042" cy="14.6416" rx="1.92725" ry="1.88771" fill="#C4C4C4" />
              </svg>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
              <li *ngIf="canEditPost(activity)">
                <a class="dropdown-item" (click)="editPost(activity)">
                  Edit Post
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="d-flex flex-column">
        <div class="d-flex my-3 fs-14" [innerHTML]="activity.content"></div>
        <div class="videp-preview-link" *ngIf="activity.link || activity.imageUrl">
          <a *ngIf="activity.link && activity.link !== 'string'" [href]="activity.link" target="_blank">{{ activity.link }}</a>
          <img *ngIf="activity.imageUrl && activity.imageUrl !== 'string'" [src]="activity.imageUrl" alt="Activity Image">
        </div>
      </div>
      <div class="fc-post-comments">
        <button (click)="toggleLike(activity)" [disabled]="!isLoggedIn">
          <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M8.52913 1.36398C9.88413 0.507312 11.6841 0.270645 13.2391 0.768979C16.6216 1.85981 17.6716 5.54731 16.7325 8.48148C15.2833 13.0898 9.09413 16.5273 8.83163 16.6715C8.7383 16.7231 8.63496 16.749 8.53163 16.749C8.4283 16.749 8.3258 16.724 8.23246 16.6731C7.97163 16.5306 1.82746 13.144 0.329964 8.48231L0.329131 8.48148C-0.61087 5.54648 0.435797 1.85815 3.81496 0.768979C5.40163 0.255645 7.1308 0.481479 8.52913 1.36398ZM4.1983 1.95898C1.46413 2.84065 0.777464 5.78148 1.51996 8.10065C2.6883 11.7356 7.30413 14.6748 8.5308 15.4023C9.76163 14.6673 14.4108 11.6956 15.5416 8.10398C16.2841 5.78231 15.595 2.84148 12.8566 1.95898C11.53 1.53315 9.98246 1.79231 8.91413 2.61898C8.6908 2.79065 8.3808 2.79398 8.1558 2.62398C7.02413 1.77315 5.5458 1.52398 4.1983 1.95898ZM12.0566 3.6139C13.1925 3.9814 13.9883 4.98723 14.0858 6.1764C14.1133 6.52056 13.8575 6.82223 13.5133 6.84973C13.4958 6.8514 13.4791 6.85223 13.4616 6.85223C13.1391 6.85223 12.8658 6.60473 12.8391 6.27806C12.7841 5.59306 12.3258 5.01473 11.6733 4.8039C11.3441 4.69723 11.1641 4.34473 11.27 4.01723C11.3775 3.6889 11.7266 3.51056 12.0566 3.6139Z"
              [attr.fill]="activity.isLiked ? '#ff0000' : '#666666'" />
          </svg>
          <span class="c-badge" *ngIf="activity.likeCount && activity.likeCount > 0">{{ activity.likeCount }}</span>
          <!-- Debug info - remove this after testing -->
          <small style="color: red; font-size: 10px; margin-left: 5px;">
            Debug: isLiked={{activity.isLiked}}, count={{activity.likeCount}}, id={{activity.likeId}}
          </small>
          <!-- Test button - remove this after testing -->
          <button style="font-size: 10px; margin-left: 5px;" (click)="testLike(activity)">Test Like</button>
        </button>
        <button>
          <svg width="18" height="21" viewBox="0 0 18 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M15.3166 3.3367C11.8652 -0.114992 6.24903 -0.11468 2.79763 3.3367C-0.653899 6.78823 -0.653899 12.4043 2.79763 15.8556C4.72832 17.7866 7.47002 18.7184 10.1774 18.3759L13.2333 20.633C13.3355 20.7085 13.4584 20.748 13.5828 20.748C13.635 20.748 13.6874 20.7412 13.7387 20.7271C13.9128 20.6791 14.0553 20.5541 14.1252 20.3877L14.9968 18.3139C15.1228 18.0143 14.982 17.6695 14.6824 17.5435C14.3827 17.4179 14.0377 17.5584 13.912 17.8579L13.3304 19.2418L10.683 17.2866C10.5567 17.1932 10.3977 17.1534 10.2431 17.1785C7.82564 17.5538 5.35453 16.7486 3.62961 15.0237C0.636951 12.031 0.636951 7.16146 3.62961 4.16865C6.62211 1.17615 11.4915 1.17586 14.4846 4.16865C16.3865 6.07085 17.1476 8.77086 16.5203 11.3912C16.5188 11.3976 16.5172 11.404 16.516 11.4105C16.4281 11.7728 16.3133 12.1306 16.178 12.4661L14.8035 15.7368C14.6775 16.0363 14.8183 16.3812 15.1179 16.5071C15.4174 16.6329 15.7625 16.4923 15.8883 16.1927L17.266 12.9143C17.4269 12.5156 17.5599 12.1004 17.6553 11.7041C17.6641 11.6763 17.6707 11.6484 17.6752 11.6204C18.3791 8.61305 17.4994 5.51944 15.3166 3.3367Z"
              fill="#666666" />
            <path
              d="M12.7863 6.83984H5.6992C5.4392 6.83984 5.22852 7.04846 5.22852 7.30846C5.22852 7.56847 5.4392 7.77708 5.6992 7.77708H12.7863C13.0463 7.77708 13.257 7.56847 13.257 7.30846C13.257 7.04846 13.0463 6.83984 12.7863 6.83984Z"
              fill="#666666" />
            <path
              d="M13.257 10.0038C13.257 9.74377 13.0463 9.53516 12.7863 9.53516H5.6992C5.4392 9.53516 5.22852 9.74377 5.22852 10.0038C5.22852 10.2638 5.4392 10.4724 5.6992 10.4724H12.7863C13.0463 10.4724 13.257 10.2638 13.257 10.0038Z"
              fill="#666666" />
            <path
              d="M5.6992 12.2295C5.4392 12.2295 5.22852 12.4381 5.22852 12.6981C5.22852 12.9581 5.4392 13.1667 5.6992 13.1667H9.75059C10.0106 13.1667 10.2213 12.9581 10.2213 12.6981C10.2213 12.4381 10.0106 12.2295 9.75059 12.2295H5.6992Z"
              fill="#666666" />
          </svg>
          <span class="c-badge">{{ activity.comments?.length || 0 }}</span>
        </button>
        <button>
          <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M8.78879 11.4709L12.2533 17.0917C12.4049 17.338 12.6417 17.3352 12.7374 17.3219C12.8331 17.3086 13.0633 17.2499 13.1467 16.9704L17.479 2.33831C17.5548 2.07968 17.4155 1.90347 17.353 1.84094C17.2924 1.77842 17.119 1.64484 16.868 1.71589L2.22542 6.00368C1.94784 6.08515 1.88721 6.31821 1.87395 6.41389C1.86069 6.51147 1.8569 6.75305 2.10227 6.90747L7.78742 10.4629L12.8104 5.38694C13.0861 5.10842 13.5361 5.10557 13.8155 5.38126C14.095 5.65694 14.0969 6.10789 13.8212 6.38642L8.78879 11.4709ZM12.6635 18.7486C12.0042 18.7486 11.3997 18.4133 11.0435 17.8373L7.37058 11.8774L1.34911 8.11157C0.700161 7.70515 0.361003 6.98136 0.466161 6.22063C0.570371 5.45989 1.09237 4.85547 1.82563 4.64042L16.4682 0.352627C17.1417 0.155575 17.8636 0.342206 18.3601 0.836732C18.8565 1.336 19.0412 2.06547 18.8404 2.74189L14.5081 17.373C14.2911 18.1092 13.6848 18.6293 12.9259 18.7306C12.8369 18.742 12.7507 18.7486 12.6635 18.7486Z"
              fill="black" fill-opacity="0.64" />
          </svg>
        </button>
      </div>
      <div class="fc-write-comment-post" *ngIf="isLoggedIn">
        <span class="cmnt-user-avatar">
          <img [src]="currentUserPhoto || userProfilePhoto" alt="User Avatar">
        </span>
        <input type="text" 
               [(ngModel)]="commentInputs[activity.id]" 
               placeholder="Write a comment" 
               (keydown)="onCommentKeyDown($event, activity)" />
      </div>
      
      <div class="activity-comments mt-3" *ngIf="activity.comments?.length">
        <div class="single-comment w-100 d-flex" *ngFor="let comment of getDisplayedComments(activity)">
          <span class="avatar">
            <img [src]="getCommentDisplayPhoto(comment)" alt="Commenter Avatar">
          </span>
          <div class="commenter-data w-100">
            <div class="d-flex justify-content-between align-items-center">
              <strong>{{ getCommentDisplayName(comment) }}</strong>
              <span class="comment-date">{{ comment.createdAt | date:'short' }}</span>
            </div>
            <div class="comment-content">{{ comment.content }}</div>
          </div>
        </div>

        <!-- Show all comments link -->
        <div class="view-comments-toggle mt-2" *ngIf="shouldShowViewAllLink(activity)">
          <a href="javascript:void(0)"
             class="view-all-comments-link"
             (click)="toggleShowAllComments(activity)">
            View all {{ activity.comments.length }} comments
          </a>
        </div>

        <!-- Show less comments link -->
        <div class="view-comments-toggle mt-2" *ngIf="shouldShowViewLessLink(activity)">
          <a href="javascript:void(0)"
             class="view-less-comments-link"
             (click)="toggleShowAllComments(activity)">
            Show less
          </a>
        </div>
      </div>
    </div>
  </div>
</div> 